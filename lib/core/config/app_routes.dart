import 'package:flutter/material.dart';
import 'package:inventory_app_recode/features/auth/view/pages/forgot_password_page.dart';
import 'package:inventory_app_recode/features/auth/view/pages/login_page.dart';
import 'package:inventory_app_recode/features/auth/view/pages/reset_password_page.dart';
import 'package:inventory_app_recode/features/auth/view/pages/signup_page.dart';
import 'package:inventory_app_recode/features/auth/view/pages/verify_opt_page.dart';

class AppRoutes {
  static const String login = '/login';
  static const String signUp = '/signup';
  static const String forgotPassword = '/forgot_password';
  static const String verifyOpt = '/verify_opt';
  static const String resetPassword = '/reset_password';

  static Route<dynamic> generateRoute(RouteSettings routeSettings) {
    final String? routeName = routeSettings.name;

    switch (routeName) {
      case login:
        return MaterialPageRoute(builder: (_) => const LoginPage());
      case signUp:
        return MaterialPageRoute(builder: (_) => const SignupPage());
      case forgotPassword:
        return MaterialPageRoute(builder: (_) => ForgotPasswordPage());
      case verifyOpt:
        final args = routeSettings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => VerifyOptPage(phoneNumber: args['phoneNumber'] as String),
        );
      case resetPassword:
        return MaterialPageRoute(builder: (_) => const ResetPasswordPage());

      default:
        return MaterialPageRoute(
          builder:
              (_) => Scaffold(
                body: Center(
                  child: Text('No route defined for ${routeSettings.name}'),
                ),
              ),
        );
    }
  }
}
