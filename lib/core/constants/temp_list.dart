import 'dart:ui';

import 'package:inventory_app_recode/core/models/category_model.dart';
import 'package:inventory_app_recode/core/models/operation_model.dart';
import 'package:inventory_app_recode/core/models/warehouse_model.dart';

class TempList {
  static List<WarehouseModel> getWarehouseListData() {
    return [
      WarehouseModel(
        id: '1',
        name: '主仓库',
        address: '上海市浦东新区张江高科技园区',
        totalItems: 450,
        totalCategories: 12,
        totalValue: 85000.0,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
      ),
      WarehouseModel(
        id: '2',
        name: '备用仓库',
        address: '上海市闵行区莘庄工业区',
        totalItems: 320,
        totalCategories: 8,
        totalValue: 42500.0,
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
      WarehouseModel(
        id: '3',
        name: '临时仓库',
        address: '上海市嘉定区安亭镇',
        totalItems: 120,
        totalCategories: 5,
        totalValue: 18000.0,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
    ];
  }

  static List<CategoryModel> getCategoryListData() {
    return [
      CategoryModel(
        id: '1',
        name: '电子设备',
        level: 1,
        warehouseId: '1',
        iconName: 'devices',
        color: const Color(0xFF5E35B1),
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        children: [
          CategoryModel(
            id: '1-1',
            name: '笔记本电脑',
            parentId: '1',
            level: 2,
            warehouseId: '1',
            iconName: 'laptop',
            color: const Color(0xFF5E35B1),
            isActive: true,
            createdAt: DateTime.now().subtract(const Duration(days: 110)),
          ),
          CategoryModel(
            id: '1-2',
            name: '手机',
            parentId: '1',
            level: 2,
            warehouseId: '1',
            iconName: 'smartphone',
            color: const Color(0xFF5E35B1),
            isActive: true,
            createdAt: DateTime.now().subtract(const Duration(days: 100)),
          ),
        ],
      ),
      CategoryModel(
        id: '2',
        name: '办公用品',
        level: 1,
        warehouseId: '1',
        iconName: 'business_center',
        color: const Color(0xFF00897B),
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
      ),
      CategoryModel(
        id: '3',
        name: '家具',
        level: 1,
        warehouseId: '1',
        iconName: 'chair',
        color: const Color(0xFFE64A19),
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 80)),
      ),
      CategoryModel(
        id: '4',
        name: '工具',
        level: 1,
        warehouseId: '2',
        iconName: 'build',
        color: const Color(0xFF7B1FA2),
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 70)),
      ),
      CategoryModel(
        id: '5',
        name: '原材料',
        level: 1,
        warehouseId: '2',
        iconName: 'inventory_2',
        color: const Color(0xFF689F38),
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
      ),
    ];
  }

  static List<OperationModel> getOperationListData() {
    return [
      OperationModel(
        id: '1',
        itemId: '101',
        itemName: '联想ThinkPad X1',
        operationType: OperationType.inbound,
        quantity: 5,
        operatorId: 'op1',
        operatorName: '张三',
        warehouseId: '1',
        warehouseName: '主仓库',
        notes: '新采购的笔记本电脑',
        operationTime: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      OperationModel(
        id: '2',
        itemId: '102',
        itemName: '苹果iPhone 15',
        operationType: OperationType.inbound,
        quantity: 10,
        operatorId: 'op1',
        operatorName: '张三',
        warehouseId: '1',
        warehouseName: '主仓库',
        notes: '新款手机入库',
        operationTime: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      OperationModel(
        id: '3',
        itemId: '101',
        itemName: '联想ThinkPad X1',
        operationType: OperationType.outbound,
        quantity: 2,
        operatorId: 'op2',
        operatorName: '李四',
        warehouseId: '1',
        warehouseName: '主仓库',
        notes: '销售出库',
        operationTime: DateTime.now().subtract(const Duration(hours: 8)),
      ),
      OperationModel(
        id: '4',
        itemId: '103',
        itemName: '办公桌',
        operationType: OperationType.edit,
        quantity: 0,
        operatorId: 'op3',
        operatorName: '王五',
        warehouseId: '1',
        warehouseName: '主仓库',
        notes: '修改了产品描述',
        operationTime: DateTime.now().subtract(const Duration(hours: 12)),
      ),
      OperationModel(
        id: '5',
        itemId: '104',
        itemName: '电动螺丝刀',
        operationType: OperationType.inbound,
        quantity: 15,
        operatorId: 'op2',
        operatorName: '李四',
        warehouseId: '2',
        warehouseName: '备用仓库',
        notes: '工具入库',
        operationTime: DateTime.now().subtract(const Duration(hours: 24)),
      ),
      OperationModel(
        id: '6',
        itemId: '105',
        itemName: '钢材',
        operationType: OperationType.outbound,
        quantity: 50,
        operatorId: 'op3',
        operatorName: '王五',
        warehouseId: '2',
        warehouseName: '备用仓库',
        notes: '生产领用',
        operationTime: DateTime.now().subtract(const Duration(hours: 36)),
      ),
      OperationModel(
        id: '7',
        itemId: '106',
        itemName: '临时设备',
        operationType: OperationType.inbound,
        quantity: 3,
        operatorId: 'op1',
        operatorName: '张三',
        warehouseId: '3',
        warehouseName: '临时仓库',
        notes: '临时设备入库',
        operationTime: DateTime.now().subtract(const Duration(hours: 48)),
      ),
    ];
  }
}
