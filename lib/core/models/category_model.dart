import 'package:flutter/material.dart';

class CategoryModel {
  final String id;
  final String name;
  final String? parentId;
  final int level;
  final String warehouseId;
  final String? iconUrl;
  final String? iconName; // 图标名称
  final Color? color; // 图标颜色
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<CategoryModel> children;

  const CategoryModel({
    required this.id,
    required this.name,
    this.parentId,
    required this.level,
    required this.warehouseId,
    this.iconUrl,
    this.iconName,
    this.color,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
    this.children = const [],
  });

  CategoryModel copyWith({
    String? id,
    String? name,
    String? parentId,
    int? level,
    String? warehouseId,
    String? iconUrl,
    String? iconName,
    Color? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<CategoryModel>? children,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      level: level ?? this.level,
      warehouseId: warehouseId ?? this.warehouseId,
      iconUrl: iconUrl ?? this.iconUrl,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      children: children ?? this.children,
    );
  }

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] as String,
      name: json['name'] as String,
      parentId: json['parent_id'] as String?,
      level: json['level'] as int? ?? 1,
      warehouseId: json['warehouse_id'] as String,
      iconUrl: json['icon_url'] as String?,
      iconName: json['icon_name'] as String?,
      color: json['color'] != null ? Color(json['color'] as int) : null,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      children:
          json['children'] != null
              ? (json['children'] as List)
                  .map((e) => CategoryModel.fromJson(e as Map<String, dynamic>))
                  .toList()
              : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'parent_id': parentId,
      'level': level,
      'warehouse_id': warehouseId,
      'icon_url': iconUrl,
      'icon_name': iconName,
      'color': color?.value,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'children': children.map((e) => e.toJson()).toList(),
    };
  }
}
