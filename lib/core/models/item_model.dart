class ItemModel {
  final String id;
  final String name;
  final String? englishName;
  final String model;
  final double price;
  final String categoryId;
  final String? categoryName;
  final String warehouseId;
  final String? supplierId;
  final String? supplierName;
  final int quantity;
  final int warningQuantity;
  final String? shelfLocation;
  final List<String> imageUrls;
  final DateTime? lastInboundDate;
  final DateTime? lastOutboundDate;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const ItemModel({
    required this.id,
    required this.name,
    this.englishName,
    required this.model,
    required this.price,
    required this.categoryId,
    this.categoryName,
    required this.warehouseId,
    this.supplierId,
    this.supplierName,
    required this.quantity,
    this.warningQuantity = 5,
    this.shelfLocation,
    this.imageUrls = const [],
    this.lastInboundDate,
    this.lastOutboundDate,
    required this.createdAt,
    this.updatedAt,
  });

  ItemModel copyWith({
    String? id,
    String? organizationId, // 新增组织ID参数
    String? name,
    String? englishName,
    String? model,
    double? price,
    String? categoryId,
    String? categoryName,
    String? warehouseId,
    String? supplierId,
    String? supplierName,
    int? quantity,
    int? warningQuantity,
    String? shelfLocation,
    List<String>? imageUrls,
    DateTime? lastInboundDate,
    DateTime? lastOutboundDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ItemModel(
      id: id ?? this.id,
      name: name ?? this.name,
      englishName: englishName ?? this.englishName,
      model: model ?? this.model,
      price: price ?? this.price,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      warehouseId: warehouseId ?? this.warehouseId,
      supplierId: supplierId ?? this.supplierId,
      supplierName: supplierName ?? this.supplierName,
      quantity: quantity ?? this.quantity,
      warningQuantity: warningQuantity ?? this.warningQuantity,
      shelfLocation: shelfLocation ?? this.shelfLocation,
      imageUrls: imageUrls ?? this.imageUrls,
      lastInboundDate: lastInboundDate ?? this.lastInboundDate,
      lastOutboundDate: lastOutboundDate ?? this.lastOutboundDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory ItemModel.fromJson(Map<String, dynamic> json) {
    return ItemModel(
      id: json['id'] as String,
      name: json['name'] as String,
      englishName: json['english_name'] as String?,
      model: json['model'] as String,
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      categoryId: json['category_id'] as String,
      categoryName: json['category_name'] as String?,
      warehouseId: json['warehouse_id'] as String,
      supplierId: json['supplier_id'] as String?,
      supplierName: json['supplier_name'] as String?,
      quantity: json['quantity'] as int? ?? 0,
      warningQuantity: json['warning_quantity'] as int? ?? 5,
      shelfLocation: json['shelf_location'] as String?,
      imageUrls:
          json['image_urls'] != null
              ? List<String>.from(json['image_urls'] as List)
              : [],
      lastInboundDate:
          json['last_inbound_date'] != null
              ? DateTime.parse(json['last_inbound_date'] as String)
              : null,
      lastOutboundDate:
          json['last_outbound_date'] != null
              ? DateTime.parse(json['last_outbound_date'] as String)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'english_name': englishName,
      'model': model,
      'price': price,
      'category_id': categoryId,
      'category_name': categoryName,
      'warehouse_id': warehouseId,
      'supplier_id': supplierId,
      'supplier_name': supplierName,
      'quantity': quantity,
      'warning_quantity': warningQuantity,
      'shelf_location': shelfLocation,
      'image_urls': imageUrls,
      'last_inbound_date': lastInboundDate?.toIso8601String(),
      'last_outbound_date': lastOutboundDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
