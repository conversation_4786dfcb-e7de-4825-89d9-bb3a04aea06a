enum OperationType { inbound, outbound, edit }

/// 操作记录模型
class OperationModel {
  final String id;
  final String itemId;
  final String itemName;
  final OperationType operationType;
  final int quantity;
  final String operatorId;
  final String operatorName;
  final String warehouseId;
  final String warehouseName;
  final String? notes;
  final DateTime operationTime;

  const OperationModel({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.operationType,
    required this.quantity,
    required this.operatorId,
    required this.operatorName,
    required this.warehouseId,
    required this.warehouseName,
    this.notes,
    required this.operationTime,
  });

  /// 获取操作类型文本
  String get operationTypeText {
    switch (operationType) {
      case OperationType.inbound:
        return '入库';
      case OperationType.outbound:
        return '出库';
      case OperationType.edit:
        return '编辑';
    }
  }

  /// 从JSON创建操作记录
  factory OperationModel.fromJson(Map<String, dynamic> json) {
    // 将字符串转换为枚举
    OperationType getOperationType(String type) {
      switch (type) {
        case 'inbound':
          return OperationType.inbound;
        case 'outbound':
          return OperationType.outbound;
        case 'edit':
          return OperationType.edit;
        default:
          return OperationType.edit;
      }
    }

    return OperationModel(
      id: json['id'] as String,
      itemId: json['item_id'] as String,
      itemName: json['item_name'] as String,
      operationType: getOperationType(json['operation_type'] as String),
      quantity: json['quantity'] as int,
      operatorId: json['operator_id'] as String,
      operatorName: json['operator_name'] as String,
      warehouseId: json['warehouse_id'] as String,
      warehouseName: json['warehouse_name'] as String,
      notes: json['notes'] as String?,
      operationTime: DateTime.parse(json['operation_time'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    // 将枚举转换为字符串
    String getOperationTypeString(OperationType type) {
      switch (type) {
        case OperationType.inbound:
          return 'inbound';
        case OperationType.outbound:
          return 'outbound';
        case OperationType.edit:
          return 'edit';
      }
    }

    return {
      'id': id,
      'item_id': itemId,
      'item_name': itemName,
      'operation_type': getOperationTypeString(operationType),
      'quantity': quantity,
      'operator_id': operatorId,
      'operator_name': operatorName,
      'warehouse_id': warehouseId,
      'warehouse_name': warehouseName,
      'notes': notes,
      'operation_time': operationTime.toIso8601String(),
    };
  }

  /// 创建操作记录副本
  OperationModel copyWith({
    String? id,
    String? itemId,
    String? itemName,
    OperationType? operationType,
    int? quantity,
    String? operatorId,
    String? operatorName,
    String? warehouseId,
    String? warehouseName,
    String? notes,
    DateTime? operationTime,
  }) {
    return OperationModel(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      operationType: operationType ?? this.operationType,
      quantity: quantity ?? this.quantity,
      operatorId: operatorId ?? this.operatorId,
      operatorName: operatorName ?? this.operatorName,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      notes: notes ?? this.notes,
      operationTime: operationTime ?? this.operationTime,
    );
  }
}
