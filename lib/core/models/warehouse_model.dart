class WarehouseModel {
  final String id;
  final String name;
  final String address;
  final int totalItems;
  final int totalCategories;
  final double totalValue;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const WarehouseModel({
    required this.id,
    required this.name,
    required this.address,
    this.totalItems = 0,
    this.totalCategories = 0,
    this.totalValue = 0.0,
    required this.createdAt,
    this.updatedAt,
  });

  factory WarehouseModel.fromJson(Map<String, dynamic> json) {
    return WarehouseModel(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      totalItems: json['total_items'] ?? 0,
      totalCategories: json['total_categories'] ?? 0,
      totalValue: json['total_value'] ?? 0.0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'total_items': totalItems,
      'total_categories': totalCategories,
      'total_value': totalValue,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
