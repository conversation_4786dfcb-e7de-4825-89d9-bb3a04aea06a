class Validators {
  // 姓名验证（2-20个字符，支持中英文及间隔符）
  static String? name(String? value) {
    if (value == null || value.isEmpty) return '姓名不能为空';
    final regex = RegExp(r'^[\u4e00-\u9fa5a-zA-Z·\s]{2,20}$');
    if (!regex.hasMatch(value)) return '请输入2-20位中英文姓名';
    return null;
  }

  // 中国大陆手机号验证
  static String? phone(String? value) {
    if (value == null || value.isEmpty) return '手机号不能为空';
    final regex = RegExp(r'^1[3-9]\d{9}$');
    if (!regex.hasMatch(value)) return '请输入有效的11位手机号';
    return null;
  }

  // 密码验证（6-18位任意字符）
  static String? password(String? value) {
    if (value == null || value.isEmpty) return '密码不能为空';
    if (value.length < 6) return '密码至少6位';
    if (value.length > 18) return '密码最多18位';
    return null;
  }

  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    if (value != password) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  // 验证码验证（6位纯数字）
  static String? verificationCode(String? value) {
    if (value == null || value.isEmpty) return '验证码不能为空';
    final regex = RegExp(r'^\d{6}$');
    if (!regex.hasMatch(value)) return '验证码必须是6位数字';
    return null;
  }

  // 价格验证（大于0，最多2位小数）
  static String? price(String? value) {
    if (value == null || value.isEmpty) return '价格不能为空';
    final regex = RegExp(r'^[1-9]\d*(\.\d{1,2})?$|^0\.\d{1,2}$');
    if (!regex.hasMatch(value)) return '请输入有效价格（大于0，最多2位小数）';
    return null;
  }

  // 数量验证（1-998的整数）
  static String? quantity(String? value) {
    if (value == null || value.isEmpty) return '数量不能为空';
    final regex = RegExp(r'^[1-9]\d{0,2}$');
    if (!regex.hasMatch(value)) return '请输入1-999的整数';
    final num = int.tryParse(value);
    if (num == null) return '请输入有效数字';
    if (num <= 0) return '数量必须大于0';
    if (num >= 999) return '数量必须小于999';
    return null;
  }
}
