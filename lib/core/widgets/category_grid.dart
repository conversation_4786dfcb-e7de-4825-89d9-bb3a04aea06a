import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/models/category_model.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';

class CategoryGrid extends StatelessWidget {
  final List<CategoryModel> categories;
  final Function(CategoryModel) onCategoryTap;

  const CategoryGrid({
    super.key,
    required this.categories,
    required this.onCategoryTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 180,
      child: GridView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.horizontal,
        physics: const BouncingScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.0,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
        ),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final color =
              AppColors.cardColors[index % AppColors.cardColors.length];
          final lightColor =
              AppColors.cardLightColors[index %
                  AppColors.cardLightColors.length];

          return GestureDetector(
            onTap: () => onCategoryTap(category),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: lightColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Center(
                    child:
                        category.iconUrl != null && category.iconUrl!.isNotEmpty
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Image.network(
                                category.iconUrl!,
                                width: 30,
                                height: 30,
                                fit: BoxFit.cover,
                                errorBuilder:
                                    (context, error, stackTrace) => Icon(
                                      Icons.category_outlined,
                                      color: color,
                                      size: 30,
                                    ),
                              ),
                            )
                            : Icon(
                              Icons.category_outlined,
                              color: color,
                              size: 30,
                            ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  category.name,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
