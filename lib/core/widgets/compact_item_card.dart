import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:inventory_app_recode/core/models/item_model.dart';
import 'package:inventory_app_recode/core/widgets/quantity_input_dialog.dart';
import 'floating_message.dart';


class CompactItemCard extends StatefulWidget {
  final ItemModel item;
  final Function(String)? onFavorite;
  final Function(String, int)? onInbound;
  final Function(String, int)? onOutbound;
  final bool isFavorite;

  const CompactItemCard({
    super.key,
    required this.item,
    this.onFavorite,
    this.onInbound,
    this.onOutbound,
    this.isFavorite = false,
  });

  @override
  State<CompactItemCard> createState() => _CompactItemCardState();
}

class _CompactItemCardState extends State<CompactItemCard> {
  late bool _isFavorite;

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.isFavorite;
  }

  // 处理收藏按钮点击
  void _handleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });

    if (widget.onFavorite != null) {
      widget.onFavorite!(widget.item.id);
    }

    // 显示悬浮提示
    showFloatingMessage(
      context,
      message: _isFavorite ? '已添加到收藏' : '已取消收藏',
      icon: _isFavorite ? Icons.favorite : Icons.favorite_border,
    );
  }

  // 处理入库按钮点击
  Future<void> _handleInbound() async {
    // 显示数量输入对话框
    final quantity = await showQuantityInputDialog(
      context: context,
      title: '入库数量',
      message: '请输入要入库的${widget.item.name}数量',
      initialValue: 1,
      confirmColor: Colors.green,
    );

    // 如果用户取消了操作，quantity 将为 null
    if (quantity != null && quantity > 0 && widget.onInbound != null) {
      widget.onInbound!(widget.item.id, quantity);

      // 显示悬浮提示
      if (mounted) {
        showFloatingMessage(
          context,
          message: '已入库 $quantity 个${widget.item.name}',
          icon: Icons.add_circle_outline,
        );
      }
    }
  }

  // 处理出库按钮点击
  Future<void> _handleOutbound() async {
    // 检查库存是否为零
    if (widget.item.quantity <= 0) {
      // 显示悬浮提示
      if (mounted) {
        showFloatingMessage(
          context,
          message: '当前 ${widget.item.name} 库存为零，无法出库',
          icon: Icons.error_outline,
          backgroundColor: Colors.red,
        );
      }
      return;
    }

    // 使用一个局部变量捕获当前的库存数量，避免在异步操作中使用可能变化的值
    final currentQuantity = widget.item.quantity;

    // 显示数量输入对话框
    final quantity = await showQuantityInputDialog(
      context: context,
      title: '出库数量',
      message: '请输入要出库的${widget.item.name}数量',
      initialValue: 1,
      maxValue: currentQuantity, // 限制最大出库数量不超过库存
      confirmColor: Colors.red,
    );

    // 如果用户取消了操作或组件已经被卸载，直接返回
    if (quantity == null ||
        quantity <= 0 ||
        widget.onOutbound == null ||
        !mounted) {
      return;
    }

    // 再次检查出库数量是否超过库存
    if (quantity > currentQuantity) {
      // 使用延迟确保不会与前一个消息冲突
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          showFloatingMessage(
            context,
            message: '出库数量 ($quantity) 超过了当前库存 ($currentQuantity)',
            icon: Icons.error_outline,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            duration: const Duration(seconds: 2),
          );
        }
      });
      return;
    }

    // 调用出库回调
    widget.onOutbound!(widget.item.id, quantity);

    // 显示悬浮提示
    // 使用延迟确保不会与前一个消息冲突
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        showFloatingMessage(
          context,
          message: '已出库 $quantity 个${widget.item.name}',
          icon: Icons.remove_circle_outline,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 获取一级分类和二级分类
    final String categoryDisplay = widget.item.categoryName ?? '未分类';
    final List<String> categories = categoryDisplay.split('-');
    final String parentCategory =
        categories.isNotEmpty ? categories[0].trim() : '';
    final String subCategory =
        categories.length > 1 ? categories[1].trim() : '';

    // 构建分类路径显示
    final String categoryPath =
        subCategory.isNotEmpty
            ? '$parentCategory > $subCategory'
            : parentCategory;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      elevation: 1,
      shadowColor: Colors.black12,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          // TODO 导航到物品详情页面
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 物品图片
              ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: SizedBox(
                  width: 45,
                  height: 45,
                  child:
                      widget.item.imageUrls.isNotEmpty
                          ? CachedNetworkImage(
                            imageUrl: widget.item.imageUrls.first,
                            fit: BoxFit.cover,
                            placeholder:
                                (context, url) => Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.image,
                                    color: Colors.grey,
                                    size: 20,
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  color: Colors.grey[200],
                                  child: const Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey,
                                    size: 20,
                                  ),
                                ),
                          )
                          : Container(
                            color: Colors.grey[200],
                            child: const Icon(
                              Icons.inventory_2_outlined,
                              color: Colors.grey,
                              size: 20,
                            ),
                          ),
                ),
              ),
              const SizedBox(width: 8),

              // 物品信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 中文名称
                    Text(
                      widget.item.name,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // 英文名称
                    if (widget.item.englishName != null &&
                        widget.item.englishName!.isNotEmpty)
                      Text(
                        widget.item.englishName!,
                        style: const TextStyle(
                          fontSize: 13,
                          color: Colors.black54,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 2),
                    // 分类路径
                    Row(
                      children: [
                        const Icon(
                          Icons.category_outlined,
                          size: 14,
                          color: Colors.black54,
                        ),
                        const SizedBox(width: 2),
                        Expanded(
                          child: Text(
                            categoryPath,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.black54,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    // 货架位置
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on_outlined,
                          size: 14,
                          color: Colors.black54,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          widget.item.shelfLocation ?? '未设置',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 4),

              // 右侧信息和操作按钮
              SizedBox(
                width: 120,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 型号
                    Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        widget.item.model,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.blue[700],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // 数量
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color:
                            widget.item.quantity <= widget.item.warningQuantity
                                ? Colors.red[50]
                                : Colors.green[50],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${widget.item.quantity}个',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color:
                              widget.item.quantity <=
                                      widget.item.warningQuantity
                                  ? Colors.red[700]
                                  : Colors.green[700],
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // 操作按钮
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 收藏按钮
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: InkWell(
                            onTap: _handleFavorite,
                            borderRadius: BorderRadius.circular(16),
                            child: Icon(
                              Icons.favorite,
                              color:
                                  _isFavorite ? Colors.red : Colors.grey[300],
                              size: 22,
                            ),
                          ),
                        ),

                        const SizedBox(width: 10),

                        // 入库按钮
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: InkWell(
                            onTap: _handleInbound,
                            borderRadius: BorderRadius.circular(16),
                            child: const Icon(
                              Icons.add_circle_outline,
                              color: Colors.green,
                              size: 22,
                            ),
                          ),
                        ),

                        const SizedBox(width: 10),

                        // 出库按钮
                        Container(
                          width: 30,
                          height: 30,
                          decoration: BoxDecoration(
                            color: Colors.red[50],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: InkWell(
                            onTap: _handleOutbound,
                            borderRadius: BorderRadius.circular(16),
                            child: const Icon(
                              Icons.remove_circle_outline,
                              color: Colors.red,
                              size: 22,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
