import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';
import 'package:inventory_app_recode/features/scanner/view/pages/flutter_camera_page.dart';

// 使用 StatefulWidget 并实现 AutomaticKeepAliveClientMixin 以保持状态
class CustomBottomNavigationBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final GlobalKey? homeKey;
  final GlobalKey? searchKey;
  final GlobalKey? addItemKey;
  final GlobalKey? settingsKey;
  final GlobalKey? scanKey;

  const CustomBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.homeKey,
    this.searchKey,
    this.addItemKey,
    this.settingsKey,
    this.scanKey,
  });

  @override
  State<CustomBottomNavigationBar> createState() =>
      _CustomBottomNavigationBarState();
}

class _CustomBottomNavigationBarState extends State<CustomBottomNavigationBar>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  // 打开扫描器界面
  Future<void> _openScannerScreen() async {
    if (!mounted) return;

    // 使用 Flutter 相机界面
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const FlutterCameraPage()));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用超类的 build 方法
    return SizedBox(
      height: 90, // 减少高度，使扫码按钮不要凸出太多
      child: Stack(
        children: [
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              height: 80, // 进一步增加高度
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildNavItem(0, Icons.home_outlined, Icons.home),
                  _buildNavItem(1, Icons.search_outlined, Icons.search),
                  // 中间留空
                  const SizedBox(width: 50), // 减小中间留空的宽度
                  _buildNavItem(2, Icons.add_circle_outline, Icons.add_circle),
                  _buildNavItem(3, Icons.settings_outlined, Icons.settings),
                ],
              ),
            ),
          ),
          // 中间凸起的扫码按钮 - 更小的尺寸
          Positioned(
            top: 10, // 向下移动，减少凸起高度
            left: 0,
            right: 0,
            child: Center(child: _buildScanButton()),
          ),
        ],
      ),
    );
  }

  Widget _buildScanButton() {
    Widget scanButton = SizedBox(
      width: 48, // 减小宽度
      height: 48, // 减小高度
      child: FloatingActionButton(
        heroTag: 'scanButton',
        elevation: 4, // 减小阴影
        onPressed: _openScannerScreen,
        backgroundColor: AppColors.primary,
        child: const Icon(
          Icons.qr_code_scanner,
          color: Colors.white,
          size: 24, // 减小图标尺寸
        ),
      ),
    );

    return scanButton;
  }

  Widget _buildNavItem(int index, IconData icon, IconData activeIcon) {
    final bool isSelected = widget.currentIndex == index;
    Widget navItem = InkWell(
      onTap: () => widget.onTap(index),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        width: 60, // 增加点击区域宽度
        height: 60, // 增加点击区域高度
        alignment: Alignment.center,
        child: Icon(
          isSelected ? activeIcon : icon,
          color: isSelected ? AppColors.primary : AppColors.textSecondary,
          size: 28, // 保持图标尺寸
        ),
      ),
    );

    return navItem;
  }
}
