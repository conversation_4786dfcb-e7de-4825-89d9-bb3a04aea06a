import 'package:flutter/material.dart';

class FloatingMessage {
  static OverlayEntry? _currentOverlay;
  static Duration? _currentDuration;

  /// 显示悬浮提示消息
  ///
  /// [context] - 当前上下文
  /// [message] - 要显示的消息文本
  /// [duration] - 消息显示的持续时间，默认为2秒
  /// [icon] - 可选的图标，显示在消息文本前面
  /// [backgroundColor] - 消息背景颜色，默认为白色
  /// [textColor] - 消息文本颜色，默认为黑色
  /// [iconColor] - 图标颜色，默认为主题色
  static void show(
    BuildContext context, {
    required String message,
    Duration duration = const Duration(seconds: 2),
    IconData? icon,
    Color backgroundColor = Colors.white,
    Color? textColor,
    Color? iconColor,
  }) {
    // 如果已经有一个悬浮消息在显示，先安全地移除它
    if (_currentOverlay != null) {
      try {
        if (_currentOverlay!.mounted) {
          _currentOverlay!.remove();
        }
      } catch (e) {
        debugPrint('Error removing previous overlay: $e');
      } finally {
        _currentOverlay = null;
      }

      // 添加一个短暂的延迟，确保前一个消息完全移除
      // 保存当前的BuildContext引用
      final currentContext = context;
      Future.delayed(const Duration(milliseconds: 50), () {
        // 使用WidgetsBinding.instance.addPostFrameCallback确保在下一帧渲染时执行
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // 检查上下文是否仍然有效
          if (currentContext.mounted) {
            _showNewMessage(
              currentContext,
              message: message,
              duration: duration,
              icon: icon,
              backgroundColor: backgroundColor,
              textColor: textColor,
              iconColor: iconColor,
            );
          }
        });
      });
      return;
    }

    _showNewMessage(
      context,
      message: message,
      duration: duration,
      icon: icon,
      backgroundColor: backgroundColor,
      textColor: textColor,
      iconColor: iconColor,
    );
  }

  static void _showNewMessage(
    BuildContext context, {
    required String message,
    required Duration duration,
    IconData? icon,
    Color backgroundColor = Colors.white,
    Color? textColor,
    Color? iconColor,
  }) {
    // 创建一个新的悬浮消息
    final overlay = OverlayEntry(
      builder:
          (context) => _FloatingMessageWidget(
            message: message,
            icon: icon,
            backgroundColor: backgroundColor,
            textColor: textColor,
            iconColor: iconColor,
          ),
    );

    // 保存当前显示的悬浮消息引用和持续时间
    _currentOverlay = overlay;
    _currentDuration = duration;

    // 将悬浮消息添加到Overlay中
    try {
      Overlay.of(context).insert(overlay);

      // 设置定时器，在指定时间后移除悬浮消息
      Future.delayed(duration, () {
        try {
          // 先检查overlay是否还有效，再尝试移除
          if (overlay.mounted) {
            overlay.remove();
          }
        } catch (e) {
          // 忽略移除过程中的错误
          debugPrint('Error removing overlay: $e');
        } finally {
          if (_currentOverlay == overlay) {
            _currentOverlay = null;
            _currentDuration = null;
          }
        }
      });
    } catch (e) {
      debugPrint('Error inserting overlay: $e');
      _currentOverlay = null;
      _currentDuration = null;
    }
  }
}

class _FloatingMessageWidget extends StatefulWidget {
  final String message;
  final IconData? icon;
  final Color backgroundColor;
  final Color? textColor;
  final Color? iconColor;

  const _FloatingMessageWidget({
    required this.message,
    this.icon,
    this.backgroundColor = Colors.white,
    this.textColor,
    this.iconColor,
  });

  @override
  State<_FloatingMessageWidget> createState() => _FloatingMessageWidgetState();
}

class _FloatingMessageWidgetState extends State<_FloatingMessageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // 创建动画控制器
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // 创建透明度动画
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
        reverseCurve: Curves.easeIn,
      ),
    );

    // 创建滑动动画
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, -0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));

    // 启动动画
    _controller.forward();

    // 在消息即将消失前，播放反向动画
    // 获取父组件中的持续时间
    final Duration messageDuration =
        FloatingMessage._currentDuration ?? const Duration(seconds: 2);

    // 计算反向动画开始时间，比消息持续时间少300毫秒
    final int reverseDelay = messageDuration.inMilliseconds - 300;

    Future.delayed(
      Duration(milliseconds: reverseDelay > 0 ? reverseDelay : 1700),
      () {
        if (mounted) {
          _controller.reverse();
        }
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 60.0),
          child: FadeTransition(
            opacity: _opacityAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Material(
                color: Colors.transparent,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24.0,
                    vertical: 12.0,
                  ),
                  decoration: BoxDecoration(
                    color: widget.backgroundColor,
                    borderRadius: BorderRadius.circular(30.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        blurRadius: 10.0,
                        spreadRadius: 1.0,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (widget.icon != null) ...[
                        Icon(
                          widget.icon,
                          color:
                              widget.iconColor ??
                              Theme.of(context).colorScheme.primary,
                          size: 20.0,
                        ),
                        const SizedBox(width: 8.0),
                      ],
                      Flexible(
                        child: Text(
                          widget.message,
                          style: TextStyle(
                            fontSize: 14.0,
                            fontWeight: FontWeight.w500,
                            color: widget.textColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 显示悬浮消息
void showFloatingMessage(
  BuildContext context, {
  required String message,
  IconData? icon,
  Duration duration = const Duration(seconds: 2),
  Color backgroundColor = Colors.white,
  Color? textColor,
  Color? iconColor,
}) {
  FloatingMessage.show(
    context,
    message: message,
    icon: icon,
    duration: duration,
    backgroundColor: backgroundColor,
    textColor: textColor,
    iconColor: iconColor,
  );
}
