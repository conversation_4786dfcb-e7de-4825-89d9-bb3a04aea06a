import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';


/// 数量输入对话框
Future<int?> showQuantityInputDialog({
  required BuildContext context,
  required String title,
  String? message,
  int initialValue = 1,
  int minValue = 1,
  int maxValue = 9999,
  Color confirmColor = AppColors.primary,
  Color cancelColor = Colors.grey,
}) async {
  return await showDialog<int>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return _QuantityInputDialog(
        title: title,
        message: message,
        initialValue: initialValue,
        minValue: minValue,
        maxValue: maxValue,
        confirmColor: confirmColor,
        cancelColor: cancelColor,
      );
    },
  );
}

class _QuantityInputDialog extends StatefulWidget {
  final String title;
  final String? message;
  final int initialValue;
  final int minValue;
  final int maxValue;
  final Color confirmColor;
  final Color cancelColor;

  const _QuantityInputDialog({
    required this.title,
    required this.message,
    required this.initialValue,
    required this.minValue,
    required this.maxValue,
    required this.confirmColor,
    required this.cancelColor,
  });

  @override
  _QuantityInputDialogState createState() => _QuantityInputDialogState();
}

class _QuantityInputDialogState extends State<_QuantityInputDialog> {
  late TextEditingController _textController;
  late FocusNode _focusNode;
  late int _quantity;
  bool _isDisposed = false;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(
      text: widget.initialValue.toString(),
    );
    _focusNode = FocusNode();
    _quantity = widget.initialValue;
  }

  @override
  void dispose() {
    _isDisposed = true;
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // 安全地获取当前数量值
  int _getCurrentValue() {
    if (_textController.text.trim().isEmpty) {
      return _quantity;
    }

    try {
      final value = int.parse(_textController.text.trim());
      return value;
    } catch (e) {
      return _quantity;
    }
  }

  // 安全地更新文本框
  void _updateTextField(int value) {
    if (!_isDisposed) {
      setState(() {
        _quantity = value;
        _textController.text = value.toString();
      });
    }
  }

  // 处理减少数量
  void _decreaseQuantity() {
    final currentValue = _getCurrentValue();
    if (currentValue > widget.minValue) {
      _updateTextField(currentValue - 1);
    }
  }

  // 处理增加数量
  void _increaseQuantity() {
    final currentValue = _getCurrentValue();
    if (currentValue < widget.maxValue) {
      _updateTextField(currentValue + 1);
    }
  }

  // 处理确认按钮
  void _handleConfirm() {
    // 如果文本框为空，使用初始值
    if (_textController.text.trim().isEmpty) {
      _textController.text = widget.initialValue.toString();
    }

    final currentValue = _getCurrentValue();

    // 检查是否超过最大值
    if (currentValue > widget.maxValue) {
      if (!_isDisposed && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('输入的数量超过了最大限制 ${widget.maxValue}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    // 检查是否小于最小值
    if (currentValue < widget.minValue) {
      if (!_isDisposed && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('输入的数量不能小于 ${widget.minValue}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return;
    }

    // 关闭弹窗并返回当前值
    if (!_isDisposed && mounted) {
      Navigator.of(context).pop(currentValue);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.message != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(
                widget.message!,
                style: const TextStyle(fontSize: 14, color: Colors.black54),
              ),
            ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 减号按钮
              IconButton(
                icon: const Icon(Icons.remove_circle_outline),
                color:
                    _getCurrentValue() > widget.minValue
                        ? AppColors.primary
                        : Colors.grey,
                onPressed:
                    _getCurrentValue() > widget.minValue
                        ? _decreaseQuantity
                        : null,
              ),
              // 数量输入框
              Container(
                width: 80,
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: TextField(
                  controller: _textController,
                  focusNode: _focusNode,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  textInputAction: TextInputAction.done,
                  onEditingComplete: () {
                    if (mounted) {
                      FocusScope.of(context).unfocus();
                    }
                  },
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 8,
                    ),
                  ),
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      try {
                        final newValue = int.parse(value);
                        if (newValue >= widget.minValue &&
                            newValue <= widget.maxValue) {
                          setState(() {
                            _quantity = newValue;
                          });
                        }
                      } catch (e) {
                        // 如果转换失败，不做任何操作
                      }
                    }
                  },
                ),
              ),
              // 加号按钮
              IconButton(
                icon: const Icon(Icons.add_circle_outline),
                color:
                    _getCurrentValue() < widget.maxValue
                        ? AppColors.primary
                        : Colors.grey,
                onPressed:
                    _getCurrentValue() < widget.maxValue
                        ? _increaseQuantity
                        : null,
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          child: Text('取消', style: TextStyle(color: widget.cancelColor)),
          onPressed: () {
            if (mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
        TextButton(
          child: Text('确定', style: TextStyle(color: widget.confirmColor)),
          onPressed: _handleConfirm,
        ),
      ],
    );
  }
}
