import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'otp_count_down_provider.g.dart';

@riverpod
class OtpCountDownProvider extends _$OtpCountDownProvider {
  @override
  int build() {
    return 0;
  }

  void startCountDown() {
    state = 60;
    _startCountDown();
  }

  void _startCountDown() {
    if (state == 0) return;
    state--;
    Future.delayed(const Duration(seconds: 1), _startCountDown);
  }
}
