// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_count_down_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$otpCountDownProviderHash() =>
    r'238c9b9b804f66001ae95f625a557128749d391d';

/// See also [OtpCountDownProvider].
@ProviderFor(OtpCountDownProvider)
final otpCountDownProviderProvider =
    AutoDisposeNotifierProvider<OtpCountDownProvider, int>.internal(
      OtpCountDownProvider.new,
      name: r'otpCountDownProviderProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$otpCountDownProviderHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$OtpCountDownProvider = AutoDisposeNotifier<int>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
