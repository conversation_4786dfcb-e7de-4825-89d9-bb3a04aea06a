// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'auth_local_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$authLocalRepositoryHash() =>
    r'4997658913f9b8ca4ef9f6396b78c449ec13991b';

/// See also [authLocalRepository].
@ProviderFor(authLocalRepository)
final authLocalRepositoryProvider = Provider<AuthLocalRepository>.internal(
  authLocalRepository,
  name: r'authLocalRepositoryProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$authLocalRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AuthLocalRepositoryRef = ProviderRef<AuthLocalRepository>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
