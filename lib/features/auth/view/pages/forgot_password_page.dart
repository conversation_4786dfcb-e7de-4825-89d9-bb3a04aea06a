import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:inventory_app_recode/core/config/app_routes.dart';
import 'package:inventory_app_recode/core/utils/validators.dart';
import 'package:inventory_app_recode/core/widgets/custom_text_field.dart';
import 'package:inventory_app_recode/core/widgets/primary_button.dart';
import 'package:inventory_app_recode/features/auth/providers/otp_count_down_provider.dart';
import 'package:inventory_app_recode/features/auth/view/widgets/auth_header.dart';

class ForgotPasswordPage extends ConsumerWidget {
  ForgotPasswordPage({super.key});

  final _formKey = GlobalKey<FormState>();

  final _phoneController = TextEditingController();

  final bool _isLoading = false;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final countDownSecond = ref.watch(otpCountDownProviderProvider);
    return Scaffold(
      appBar: AppBar(title: const Text('找回密码')),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(title: '验证手机号'),
                  const SizedBox(height: 32),
                  CustomTextField(
                    controller: _phoneController,
                    labelText: '手机号',
                    prefixIcon: Icons.phone_android,
                    keyboardType: TextInputType.phone,
                    validator: Validators.phone,
                  ),
                  const SizedBox(height: 24),
                  PrimaryButton(
                    text: '发送验证码',
                    isLoading: _isLoading,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        if (countDownSecond == 0) {
                          ref
                              .read(otpCountDownProviderProvider.notifier)
                              .startCountDown();
                        }
                        Navigator.of(context).pushNamed(
                          AppRoutes.verifyOpt,
                          arguments: {'phoneNumber': _phoneController.text},
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('记起密码了？'),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('返回登录'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
