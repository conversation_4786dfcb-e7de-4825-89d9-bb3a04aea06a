import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/utils/validators.dart';
import 'package:inventory_app_recode/core/widgets/custom_text_field.dart';
import 'package:inventory_app_recode/core/widgets/primary_button.dart';
import 'package:inventory_app_recode/features/auth/view/widgets/auth_header.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('重置密码')),
      body: Safe<PERSON><PERSON>(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(title: '重置密码'),
                  const SizedBox(height: 32),
                  CustomTextField(
                    controller: _passwordController,
                    labelText: '新密码',
                    prefixIcon: Icons.lock_outline,
                    obscureText: true,
                    validator: Validators.password,
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _confirmPasswordController,
                    labelText: '确认新密码',
                    prefixIcon: Icons.lock_outline,
                    obscureText: true,
                    validator:
                        (value) => Validators.validateConfirmPassword(
                          value,
                          _passwordController.text,
                        ),
                  ),
                  const SizedBox(height: 24),
                  PrimaryButton(
                    text: '重置密码',
                    isLoading: _isLoading,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // TODO 处理重置密码的逻辑
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
