import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:inventory_app_recode/core/config/app_routes.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';
import 'package:inventory_app_recode/core/utils/validators.dart';
import 'package:inventory_app_recode/core/widgets/custom_text_field.dart';
import 'package:inventory_app_recode/core/widgets/primary_button.dart';
import 'package:inventory_app_recode/features/auth/providers/otp_count_down_provider.dart';
import 'package:inventory_app_recode/features/auth/view/widgets/auth_header.dart';

class SignupPage extends ConsumerStatefulWidget {
  const SignupPage({super.key});

  @override
  ConsumerState<SignupPage> createState() => _SignupPageState();
}

class _SignupPageState extends ConsumerState<SignupPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _optController = TextEditingController();
  final bool _isLoading = false;
  bool _isSendingOtp = false;

  @override
  void dispose() {
    _optController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final countDownSecond = ref.watch(otpCountDownProviderProvider);

    if (countDownSecond == 0) {
      _isSendingOtp = false;
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('注册', style: TextStyle(fontWeight: FontWeight.w600)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(title: '创建账户', subtitle: '请填写以下信息完成注册'),
                  const SizedBox(height: 32),
                  CustomTextField(
                    controller: _nameController,
                    labelText: '姓名',
                    prefixIcon: Icons.person_outline,
                    validator: Validators.name,
                  ),
                  const SizedBox(height: 20),
                  CustomTextField(
                    controller: _phoneController,
                    labelText: '手机号',
                    prefixIcon: Icons.phone_android,
                    keyboardType: TextInputType.phone,
                    validator: Validators.phone,
                  ),
                  const SizedBox(height: 20),
                  CustomTextField(
                    controller: _passwordController,
                    labelText: '密码',
                    prefixIcon: Icons.lock_outline,
                    obscureText: true,
                    validator: Validators.password,
                  ),
                  const SizedBox(height: 20),
                  CustomTextField(
                    controller: _confirmPasswordController,
                    labelText: '确认密码',
                    prefixIcon: Icons.lock_outline,
                    obscureText: true,
                    validator:
                        (value) => Validators.validateConfirmPassword(
                          value,
                          _passwordController.text,
                        ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        flex: 2,
                        child: CustomTextField(
                          controller: _optController,
                          labelText: '验证码',
                          prefixIcon: Icons.sms_outlined,
                          keyboardType: TextInputType.number,
                          validator: Validators.verificationCode,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 1,
                        child: SizedBox(
                          height: 56,
                          child: ElevatedButton(
                            onPressed:
                                _isSendingOtp
                                    ? null
                                    : () {
                                      ref
                                          .read(
                                            otpCountDownProviderProvider
                                                .notifier,
                                          )
                                          .startCountDown();
                                      _isSendingOtp = true;
                                      // TODO 发送验证码的逻辑
                                    },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primary,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child:
                                _isSendingOtp
                                    ? Text('$countDownSecond秒后重试')
                                    : Text(
                                      '发送验证码',
                                      style: TextStyle(fontSize: 13),
                                    ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 32),
                  PrimaryButton(
                    text: '创建账户',
                    isLoading: _isLoading,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // TODO 处理注册逻辑
                      }
                    },
                  ),
                  const SizedBox(height: 32),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        '已经有账户？',
                        style: TextStyle(color: AppColors.textSecondary),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pushNamed(context, AppRoutes.login);
                        },
                        child: const Text('点击登录'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
