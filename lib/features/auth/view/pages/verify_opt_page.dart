import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:inventory_app_recode/core/utils/validators.dart';
import 'package:inventory_app_recode/core/widgets/custom_text_field.dart';
import 'package:inventory_app_recode/core/widgets/primary_button.dart';
import 'package:inventory_app_recode/features/auth/providers/otp_count_down_provider.dart';
import 'package:inventory_app_recode/features/auth/view/widgets/auth_header.dart';

class VerifyOptPage extends ConsumerStatefulWidget {
  final String phoneNumber;
  const VerifyOptPage({super.key, required this.phoneNumber});

  @override
  ConsumerState<VerifyOptPage> createState() => _VerifyOptPageState();
}

class _VerifyOptPageState extends ConsumerState<VerifyOptPage> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  final bool _isLoading = false;
  bool _isSendingOtp = true;

  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final countDownSecond = ref.watch(otpCountDownProviderProvider);

    if (countDownSecond == 0) {
      _isSendingOtp = false;
    }

    return Scaffold(
      appBar: AppBar(title: const Text('验证码')),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const AuthHeader(title: '输入验证码'),
                  const SizedBox(height: 16),
                  Text(
                    '验证码已发送至 ${widget.phoneNumber}',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 32),
                  CustomTextField(
                    controller: _otpController,
                    labelText: '验证码',
                    prefixIcon: Icons.sms_outlined,
                    keyboardType: TextInputType.number,
                    validator: Validators.verificationCode,
                  ),
                  const SizedBox(height: 24),
                  PrimaryButton(
                    text: '验证',
                    isLoading: _isLoading,
                    onPressed: () {
                      if (_formKey.currentState!.validate()) {
                        // TODO 处理验证码的验证
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton(
                        onPressed:
                            _isSendingOtp
                                ? null
                                : () {
                                  ref
                                      .read(
                                        otpCountDownProviderProvider.notifier,
                                      )
                                      .startCountDown();
                                  _isSendingOtp = true;
                                  // TODO 发送验证码的逻辑
                                },
                        child:
                            _isSendingOtp
                                ? Text(
                                  '没有收到验证码？$countDownSecond秒后重新发送',
                                  style: TextStyle(color: Colors.grey),
                                )
                                : Text('重新发送验证码'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
