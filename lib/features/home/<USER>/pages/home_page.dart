import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/constants/temp_list.dart';
import 'package:inventory_app_recode/core/models/category_model.dart';
import 'package:inventory_app_recode/core/models/operation_model.dart';
import 'package:inventory_app_recode/core/models/warehouse_model.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';
import 'package:inventory_app_recode/core/widgets/category_grid.dart';
import 'package:inventory_app_recode/features/home/<USER>/widgets/compact_dashboard_card.dart';
import 'package:inventory_app_recode/features/home/<USER>/widgets/recent_items_list.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String _selectedWarehouseId = '';
  String _selectedWarehouseName = '';
  bool _isLoading = false;
  final List<WarehouseModel> _warehouses = TempList.getWarehouseListData();
  final List<CategoryModel> _categories = TempList.getCategoryListData();
  final List<OperationModel> _recentOperations =
      TempList.getOperationListData();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primaryLight,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.inventory_2_outlined,
                color: AppColors.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '库存管理系统',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
              ],
            ),
          ],
        ),
        actions: [
          Stack(
            alignment: Alignment.center,
            children: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {
                  // TODO 导航到通知页面
                },
              ),
              Positioned(
                top: 10,
                right: 10,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(width: 8),
        ],
        elevation: 0,
        backgroundColor: AppColors.background,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: () async {
                  // TODO 刷新数据
                  return;
                },
                color: AppColors.primary,
                backgroundColor: AppColors.cardBackground,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Warehouse selector
                        Container(
                          height: 40,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 0,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.cardBackground,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: AppColors.divider),
                          ),
                          child:
                              _warehouses.isEmpty
                                  ? const Center(child: Text('没有可用的仓库'))
                                  : DropdownButton<String>(
                                    value:
                                        _selectedWarehouseId.isEmpty
                                            ? null
                                            : _selectedWarehouseId,
                                    isExpanded: true,
                                    icon: const Icon(
                                      Icons.arrow_drop_down,
                                      color: AppColors.primary,
                                      size: 20,
                                    ),
                                    underline: Container(height: 0),
                                    dropdownColor: AppColors.cardBackground,
                                    borderRadius: BorderRadius.circular(12),
                                    hint: const Text('选择仓库'),
                                    onChanged: (String? newValue) {
                                      if (newValue != null) {
                                        setState(() {
                                          _selectedWarehouseId = newValue;
                                          _selectedWarehouseName =
                                              _warehouses
                                                  .firstWhere(
                                                    (w) => w.id == newValue,
                                                  )
                                                  .name;
                                        });
                                        // TODO 加载仓库数据
                                      }
                                    },
                                    items:
                                        _warehouses.map<
                                          DropdownMenuItem<String>
                                        >((WarehouseModel warehouse) {
                                          return DropdownMenuItem<String>(
                                            value: warehouse.id,
                                            child: Row(
                                              children: [
                                                const Icon(
                                                  Icons.warehouse_outlined,
                                                  color: AppColors.primary,
                                                  size: 16,
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  warehouse.name,
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: 14,
                                                    color:
                                                        AppColors.textPrimary,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }).toList(),
                                  ),
                        ),

                        const SizedBox(height: 16),

                        // TODO 欢迎卡片
                        const SizedBox(height: 16),

                        // Dashboard cards
                        Column(
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: CompactDashboardCard(
                                    title: '物品总数',
                                    value: '1,280',
                                    icon: Icons.inventory_2_outlined,
                                    color: AppColors.primary,
                                    percentageChange: 5.2,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: CompactDashboardCard(
                                    title: '物品总金额',
                                    value: '¥125,680',
                                    icon: Icons.attach_money,
                                    color: AppColors.secondary,
                                    percentageChange: 3.8,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Row(
                              children: [
                                Expanded(
                                  child: CompactDashboardCard(
                                    title: '本月出库金额',
                                    value: '¥53,250',
                                    icon: Icons.arrow_upward,
                                    color: AppColors.error,
                                    percentageChange: -2.5,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: CompactDashboardCard(
                                    title: '本月入库金额',
                                    value: '¥84,820',
                                    icon: Icons.arrow_downward,
                                    color: AppColors.warning,
                                    percentageChange: 8.3,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 16),

                        // Categories
                        const Text(
                          '分类',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        CategoryGrid(
                          categories: _categories,
                          onCategoryTap: (_) {
                            // TODO 处理点击分类图标的逻辑
                          },
                        ),

                        const SizedBox(height: 24),

                        // Recent items
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              '最近操作',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            TextButton.icon(
                              onPressed: () {
                                // TODO 导航到操作历史记录页面
                              },
                              icon: const Icon(Icons.list, size: 16),
                              label: const Text('查看全部'),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        RecentItemsList(
                          operations: _recentOperations,
                          onFavorite: (_) {
                            // TODO 处理收藏按钮逻辑
                          },
                          onInbound: (_, _) {
                            // TODO 处理入库逻辑
                          },
                          onOutbound: (_, _) {
                            // TODO 处理出库逻辑
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }
}
