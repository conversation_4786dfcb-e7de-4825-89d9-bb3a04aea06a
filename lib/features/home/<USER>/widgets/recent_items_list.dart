import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_app_recode/core/models/operation_model.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';

class RecentItemsList extends StatelessWidget {
  final List<OperationModel> operations;
  final Function(String)? onFavorite;
  final Function(String, int)? onInbound;
  final Function(String, int)? onOutbound;

  const RecentItemsList({
    super.key,
    required this.operations,
    this.onFavorite,
    this.onInbound,
    this.onOutbound,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: operations.length,
      itemBuilder: (context, index) {
        final operation = operations[index];
        return _buildOperationCard(context, operation);
      },
    );
  }

  Widget _buildOperationCard(BuildContext context, OperationModel operation) {
    final Color operationColor = _getOperationColor(operation.operationType);
    final Color bgColor = _getOperationBgColor(operation.operationType);
    final IconData operationIcon = _getOperationIcon(operation.operationType);
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = dateFormat.format(operation.operationTime);

    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppColors.divider),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          // TODO 导航到对应的物品详情
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Operation icon
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: bgColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(operationIcon, color: operationColor, size: 20),
              ),
              const SizedBox(width: 12),
              // Operation details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          operation.itemName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: bgColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            operation.operationTypeText,
                            style: TextStyle(
                              color: operationColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        if (operation.operationType != OperationType.edit) ...[
                          const SizedBox(width: 8),
                          Text(
                            '${operation.quantity}个',
                            style: const TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.person_outline,
                          size: 14,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          operation.operatorName,
                          style: const TextStyle(
                            color: AppColors.textSecondary,
                            fontSize: 14,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          formattedDate,
                          style: const TextStyle(
                            color: AppColors.textLight,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getOperationColor(OperationType type) {
    switch (type) {
      case OperationType.inbound:
        return AppColors.success;
      case OperationType.outbound:
        return AppColors.error;
      case OperationType.edit:
        return AppColors.info;
    }
  }

  Color _getOperationBgColor(OperationType type) {
    switch (type) {
      case OperationType.inbound:
        return AppColors.success.withAlpha(20);
      case OperationType.outbound:
        return AppColors.error.withAlpha(20);
      case OperationType.edit:
        return AppColors.info.withAlpha(20);
    }
  }

  IconData _getOperationIcon(OperationType type) {
    switch (type) {
      case OperationType.inbound:
        return Icons.add_box_outlined;
      case OperationType.outbound:
        return Icons.indeterminate_check_box_outlined;
      case OperationType.edit:
        return Icons.edit_outlined;
    }
  }
}
