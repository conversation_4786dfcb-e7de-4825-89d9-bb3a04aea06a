class ScanResultModel {
  final String title;
  final String? subtitle;
  final double similarity; // 相似度，范围 0.0-1.0
  final String? imageUrl;
  final Map<String, dynamic>? data; // 额外数据

  ScanResultModel({
    required this.title,
    this.subtitle,
    required this.similarity,
    this.imageUrl,
    this.data,
  });

  // 创建一个模拟的条码扫描结果
  static ScanResultModel createBarcodeResult(String code) {
    return ScanResultModel(
      title: '达尔优机械键盘',
      subtitle: '型号: DK100 RGB',
      similarity: 1.0, // 条码扫描是精确匹配
      data: {
        'code': code,
        'type': 'barcode',
        'category': '电子设备',
        'subcategory': '键盘',
      },
    );
  }

  // 创建模拟的物品识别结果列表
  static List<ScanResultModel> createObjectResults() {
    return [
      ScanResultModel(
        title: '达尔优机械键盘',
        subtitle: '型号: DK100 RGB',
        similarity: 0.92,
        data: {
          'type': 'object',
          'category': '电子设备',
          'subcategory': '键盘',
        },
      ),
      ScanResultModel(
        title: '罗技无线键盘',
        subtitle: '型号: K380',
        similarity: 0.78,
        data: {
          'type': 'object',
          'category': '电子设备',
          'subcategory': '键盘',
        },
      ),
      ScanResultModel(
        title: '雷蛇黑寡妇键盘',
        subtitle: '型号: BlackWidow V3',
        similarity: 0.65,
        data: {
          'type': 'object',
          'category': '电子设备',
          'subcategory': '键盘',
        },
      ),
    ];
  }
}
