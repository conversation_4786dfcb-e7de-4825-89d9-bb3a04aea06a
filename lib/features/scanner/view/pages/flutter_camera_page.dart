import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';
import 'package:inventory_app_recode/core/widgets/floating_message.dart';
import 'package:inventory_app_recode/features/scanner/model/scan_result_model.dart';
import 'package:inventory_app_recode/features/scanner/view/pages/scan_result_page.dart';
import 'package:inventory_app_recode/features/scanner/view/widgets/scan_animation.dart';
import 'package:inventory_app_recode/features/scanner/view/widgets/scan_results_widget.dart';

class FlutterCameraPage extends StatefulWidget {
  const FlutterCameraPage({super.key});

  @override
  State<FlutterCameraPage> createState() => _FlutterCameraPageState();
}

class _FlutterCameraPageState extends State<FlutterCameraPage> {
  // 相机控制器
  CameraController? _controller;
  List<CameraDescription>? _cameras;
  bool _isInitialized = false;

  // 识别状态
  bool _isRecognizing = false;

  // 识别结果
  List<ScanResultModel> _scanResults = [];
  bool _showResult = false;
  bool _isBarcodeResult = false;

  // 识别计时器
  int _recognitionCounter = 0;

  // 扫描动画是否显示
  bool _showScanAnimation = true;

  @override
  void initState() {
    super.initState();
    _initCamera();

    // 延迟2秒后开始识别
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _startRealtimeRecognition();
      }
    });
  }

  Future<void> _initCamera() async {
    try {
      // 获取可用相机列表
      _cameras = await availableCameras();
      if (_cameras != null && _cameras!.isNotEmpty) {
        // 使用后置相机
        _controller = CameraController(
          _cameras![0],
          ResolutionPreset.medium,
          enableAudio: false,
        );

        // 初始化相机
        await _controller!.initialize();

        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
        }
      }
    } catch (e) {
      debugPrint('相机初始化错误: $e');
      if (mounted) {
        showFloatingMessage(
          context,
          message: '相机初始化失败，请检查权限设置',
          icon: Icons.error_outline,
        );
      }
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  // 开始实时识别
  Future<void> _startRealtimeRecognition() async {
    if (_isRecognizing || !mounted) return;

    setState(() {
      _isRecognizing = true;
    });

    try {
      // 每3秒尝试识别一次
      _recognitionCounter++;

      // 先尝试识别条码
      final bool hasBarcodeResult = _simulateBarcodeScan();

      if (hasBarcodeResult) {
        // 条码识别成功 - 只显示一条结果
        setState(() {
          _scanResults = [ScanResultModel.createBarcodeResult('123456789')];
          _isBarcodeResult = true;
          _showResult = true;
          _showScanAnimation = false;
        });
      } else {
        // 条码识别失败，尝试物品识别
        final bool hasObjectResult = _simulateObjectRecognition();

        if (hasObjectResult) {
          // 物品识别成功 - 显示三条结果
          setState(() {
            _scanResults = ScanResultModel.createObjectResults();
            _isBarcodeResult = false;
            _showResult = true;
            _showScanAnimation = false;
          });
        } else {
          // 没有识别到任何内容
          // 只在第一次识别失败时显示提示
          if (_recognitionCounter <= 1) {
            if (mounted) {
              showFloatingMessage(
                context,
                message: '将物品放入框内进行识别',
                icon: Icons.info_outline,
                duration: const Duration(seconds: 2),
              );
            }
          }
          setState(() {
            _scanResults = [];
            _showResult = false;
            _showScanAnimation = true;
          });
        }
      }
    } finally {
      setState(() {
        _isRecognizing = false;
      });

      // 继续下一次识别
      if (mounted && !_showResult) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) {
            _startRealtimeRecognition();
          }
        });
      }
    }
  }

  // 查看识别结果详情
  void _handleResultTap(ScanResultModel result) {
    // 根据识别结果类型导航到不同的结果页面
    final scanType = _isBarcodeResult ? ScanType.barcode : ScanType.object;
    final List<String> labels =
        result.data?['category'] != null
            ? [
              result.data!['category'].toString(),
              result.data!['subcategory'].toString(),
            ]
            : ['键盘', '电子设备'];

    _navigateToResultScreen(scanType, result.title, labels: labels).then((_) {
      // 返回扫描页面后，重新开始扫描
      if (mounted) {
        setState(() {
          _showResult = false;
          _showScanAnimation = true;
          _scanResults = [];
          _recognitionCounter = 0;
        });

        // 重新开始识别
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _startRealtimeRecognition();
          }
        });
      }
    });
  }

  // 模拟条码扫描结果
  bool _simulateBarcodeScan() {
    // 这里可以根据需要调整成功率
    // 当前设置为 30% 的概率识别成功
    return (DateTime.now().millisecondsSinceEpoch % 10) < 3;
  }

  // 模拟物品识别结果
  bool _simulateObjectRecognition() {
    // 这里可以根据需要调整成功率
    // 当前设置为 70% 的概率识别成功
    return (DateTime.now().millisecondsSinceEpoch % 10) < 7;
  }

  // 导航到结果页面
  Future<dynamic> _navigateToResultScreen(
    ScanType type,
    String result, {
    List<String>? labels,
  }) {
    return Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ScanResultPage(
              scanType: type,
              scanResult: result,
              labels: labels,
            ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Column(
        children: [
          // 顶部安全区域和返回按钮
          Container(
            color: Colors.black,
            child: SafeArea(
              bottom: false,
              child: Row(
                children: [
                  // 返回按钮
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  const Spacer(),
                  // 取消按钮
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      '取消',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
              ),
            ),
          ),

          // 相机预览
          Expanded(
            child: Container(
              color: Colors.black,
              child: Stack(
                children: [
                  // 相机预览
                  if (_isInitialized && _controller != null)
                    Center(child: CameraPreview(_controller!))
                  else
                    const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),

                  // 扫描动画 - 覆盖整个画面
                  if (_showScanAnimation)
                    SizedBox.expand(
                      child: ScanAnimation(
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.height,
                        color: AppColors.primary,
                        showBorder: false,
                      ),
                    ),

                  // 识别结果显示
                  if (_showResult && _scanResults.isNotEmpty)
                    Positioned(
                      bottom: 20,
                      left: 20,
                      right: 20,
                      child: ScanResultsWidget(
                        results: _scanResults,
                        onResultTap: _handleResultTap,
                        isBarcode: _isBarcodeResult,
                      ),
                    ),

                  // 底部文字提示
                  if (!_showResult)
                    Positioned(
                      bottom: MediaQuery.of(context).size.height * 0.3,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withAlpha(100),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '将物品放入画面进行识别',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                              shadows: [
                                Shadow(
                                  offset: const Offset(1, 1),
                                  blurRadius: 3,
                                  color: Colors.black.withAlpha(128),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // 底部操作区域
          Container(
            color: Colors.black,
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Center(
              child: GestureDetector(
                onTap: () {
                  // 这里可以添加从相册选择图片的功能
                  showFloatingMessage(
                    context,
                    message: '从相册选择图片功能待实现',
                    icon: Icons.photo_library,
                  );
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.photo_library,
                      color: Colors.white.withAlpha(204),
                      size: 32,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '相册',
                      style: TextStyle(
                        color: Colors.white.withAlpha(204),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
