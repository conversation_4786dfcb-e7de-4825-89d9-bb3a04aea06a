import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/models/item_model.dart';
import 'package:inventory_app_recode/core/theme/app_colors.dart';
import 'package:inventory_app_recode/core/widgets/compact_item_card.dart';

enum ScanType { barcode, object }

enum OperationType { inbound, outbound }

class ScanResultPage extends StatefulWidget {
  final ScanType scanType;
  final String scanResult;
  final List<String>? labels;

  const ScanResultPage({
    super.key,
    required this.scanType,
    required this.scanResult,
    this.labels,
  });

  @override
  State<ScanResultPage> createState() => _ScanResultPageState();
}

class _ScanResultPageState extends State<ScanResultPage> {
  bool _isLoading = true;
  List<ItemModel> _matchedItems = [];
  String _errorMessage = '';

  // 用于存储扫描结果的相似度信息
  final Map<String, double> _itemSimilarities = {};

  @override
  void initState() {
    super.initState();
    _findMatchingItems();
  }

  Future<void> _findMatchingItems() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // 模拟从数据库获取匹配的物品
      await Future.delayed(const Duration(seconds: 1));

      // 这里是模拟数据，实际应用中应该从数据库查询
      final items = [
        ItemModel(
          id: '1',
          name: '笔记本电脑',
          englishName: 'Laptop',
          model: 'MacBook Pro',
          price: 9999.0,
          quantity: 5,
          warningQuantity: 2,
          categoryId: '1',
          categoryName: '电子设备',
          warehouseId: '1',
          supplierId: '1',
          supplierName: '苹果公司',
          shelfLocation: 'A-1-1',
          imageUrls: ['https://example.com/laptop.jpg'],
          createdAt: DateTime.now(),
        ),
        ItemModel(
          id: '2',
          name: '显示器',
          englishName: 'Monitor',
          model: 'Dell U2720Q',
          price: 3999.0,
          quantity: 3,
          warningQuantity: 1,
          categoryId: '1',
          categoryName: '电子设备',
          warehouseId: '1',
          supplierId: '2',
          supplierName: '戴尔公司',
          shelfLocation: 'A-1-2',
          imageUrls: ['https://example.com/monitor.jpg'],
          createdAt: DateTime.now(),
        ),
        ItemModel(
          id: '3',
          name: '键盘',
          englishName: 'Keyboard',
          model: 'Logitech MX Keys',
          price: 799.0,
          quantity: 10,
          warningQuantity: 3,
          categoryId: '1',
          categoryName: '电子设备',
          warehouseId: '1',
          supplierId: '3',
          supplierName: '罗技公司',
          shelfLocation: 'A-2-1',
          imageUrls: ['https://example.com/keyboard.jpg'],
          createdAt: DateTime.now(),
        ),
      ];

      if (widget.scanType == ScanType.barcode) {
        // 模拟条形码匹配
        // 实际应用中应该根据条形码查询数据库

        // 对于条码扫描，我们应该至少返回一个物品
        // 这里我们模拟返回一个键盘物品
        _matchedItems = [
          items.firstWhere(
            (item) =>
                item.name.contains('键盘') ||
                (item.englishName?.contains('Keyboard') ?? false),
            orElse: () => items[0], // 如果没有找到键盘，则返回第一个物品
          ),
        ];

        // 条码扫描是精确匹配，设置相似度为100%
        for (var item in _matchedItems) {
          _itemSimilarities[item.id] = 1.0;
        }
      } else {
        // 模拟物品识别匹配
        // 实际应用中应该根据识别标签查询数据库
        final labels = widget.labels ?? [];
        final List<ItemModel> matchedItems = [];

        // 为每个匹配的物品计算相似度
        for (var item in items) {
          double similarity = 0.0;

          // 简单的相似度计算逻辑
          for (var label in labels) {
            final String labelLower = label.toLowerCase();
            final String nameLower = item.name.toLowerCase();
            final String englishNameLower =
                item.englishName?.toLowerCase() ?? '';

            if (nameLower.contains(labelLower) ||
                englishNameLower.contains(labelLower)) {
              // 根据匹配程度计算相似度
              double matchScore = 0.0;

              // 名称完全匹配得分高
              if (nameLower == labelLower || englishNameLower == labelLower) {
                matchScore = 0.95;
              }
              // 名称开头匹配得分中等
              else if (nameLower.startsWith(labelLower) ||
                  englishNameLower.startsWith(labelLower)) {
                matchScore = 0.85;
              }
              // 名称包含匹配得分较低
              else {
                matchScore = 0.65;
              }

              // 取最高的相似度
              similarity = similarity < matchScore ? matchScore : similarity;
            }
          }

          // 如果相似度大于0，则添加到匹配列表
          if (similarity > 0) {
            matchedItems.add(item);
            _itemSimilarities[item.id] = similarity;
          }
        }

        // 按相似度排序
        matchedItems.sort(
          (a, b) => (_itemSimilarities[b.id] ?? 0).compareTo(
            _itemSimilarities[a.id] ?? 0,
          ),
        );

        _matchedItems = matchedItems;
      }

      setState(() {
        _isLoading = false;
        if (_matchedItems.isEmpty) {
          _errorMessage = '未找到匹配的物品';
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '查询物品失败: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.scanType == ScanType.barcode ? '条码扫描结果' : '物品识别结果'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage.isNotEmpty
              ? _buildErrorView()
              : _buildResultList(),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppColors.error),
          const SizedBox(height: 16),
          Text(
            _errorMessage,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _findMatchingItems,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildResultList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.scanType == ScanType.barcode ? '扫描到的条码:' : '识别结果:',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(widget.scanResult, style: const TextStyle(fontSize: 14)),
              const SizedBox(height: 16),
              const Text(
                '匹配的物品:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            itemCount: _matchedItems.length,
            itemBuilder: (context, index) {
              final item = _matchedItems[index];
              // 构建物品卡片
              return CompactItemCard(
                item: item,
                onFavorite: (_) {
                  // 收藏功能
                  // 不需要显示悬浮通知，因为 CompactItemCard 内部已经处理了
                },
                // 入库操作
                onInbound: (id, quantity) {
                  // 直接返回扫描页面
                  // 不需要显示悬浮通知，因为 CompactItemCard 内部已经处理了
                  Navigator.of(context).pop();
                },
                // 出库操作
                onOutbound: (id, quantity) {
                  // 直接返回扫描页面
                  // 不需要显示悬浮通知，因为 CompactItemCard 内部已经处理了
                  Navigator.of(context).pop();
                },
                isFavorite: false, // 这里可以根据实际情况设置是否收藏
              );
            },
          ),
        ),
      ],
    );
  }
}
