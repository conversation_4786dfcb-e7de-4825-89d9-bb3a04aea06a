import 'package:flutter/material.dart';

class ScanAnimation extends StatefulWidget {
  final double width;
  final double height;
  final Color color;
  final bool showBorder;

  const ScanAnimation({
    super.key,
    required this.width,
    required this.height,
    this.color = Colors.blue,
    this.showBorder = true,
  });

  @override
  State<ScanAnimation> createState() => _ScanAnimationState();
}

class _ScanAnimationState extends State<ScanAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0,
      end: widget.height,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    // 循环播放动画
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration:
          widget.showBorder
              ? BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: widget.color.withAlpha(30),
                  width: 1.0,
                ),
              )
              : null,
      clipBehavior: widget.showBorder ? Clip.antiAlias : Clip.none,
      child: Stack(
        children: [
          // 半透明遮罩
          if (widget.showBorder) Container(color: Colors.white.withAlpha(5)),
          // 扫描线动画
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: Size(widget.width, widget.height),
                painter: ScanLinePainter(
                  linePosition: _animation.value,
                  color: widget.color,
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class ScanLinePainter extends CustomPainter {
  final double linePosition;
  final Color color;

  ScanLinePainter({required this.linePosition, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    // 绘制扫描线
    final Paint linePaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0;

    // 绘制扫描线
    final Path path = Path();
    path.moveTo(0, linePosition);
    path.lineTo(size.width, linePosition);
    canvas.drawPath(path, linePaint);

    // 绘制扫描线的光晕效果
    final Paint glowPaint =
        Paint()
          ..color = color.withAlpha(30)
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 25.0);

    final Rect glowRect = Rect.fromLTWH(0, linePosition - 40, size.width, 80);
    canvas.drawRect(glowRect, glowPaint);

    // 绘制中间区域的增强光晕
    final Paint centerLinePaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    // 绘制中间的一条更细的线
    final Path centerPath = Path();
    centerPath.moveTo(0, linePosition - 3);
    centerPath.lineTo(size.width, linePosition - 3);
    canvas.drawPath(centerPath, centerLinePaint);

    final Path centerPath2 = Path();
    centerPath2.moveTo(0, linePosition + 3);
    centerPath2.lineTo(size.width, linePosition + 3);
    canvas.drawPath(centerPath2, centerLinePaint);
  }

  @override
  bool shouldRepaint(ScanLinePainter oldDelegate) {
    return oldDelegate.linePosition != linePosition;
  }
}
