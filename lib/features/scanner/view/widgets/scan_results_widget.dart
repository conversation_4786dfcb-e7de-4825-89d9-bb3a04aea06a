import 'package:flutter/material.dart';
import 'package:inventory_app_recode/features/scanner/model/scan_result_model.dart';

class ScanResultsWidget extends StatelessWidget {
  final List<ScanResultModel> results;
  final Function(ScanResultModel) onResultTap;
  final bool isBarcode;

  const ScanResultsWidget({
    super.key,
    required this.results,
    required this.onResultTap,
    this.isBarcode = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(51),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Text(
            isBarcode ? '条码识别结果' : '物品识别结果',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // 结果列表
          ...results
              .map((result) => _buildResultItem(context, result))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildResultItem(BuildContext context, ScanResultModel result) {
    // 计算相似度百分比
    final int similarityPercent = (result.similarity * 100).round();

    return InkWell(
      onTap: () => onResultTap(result),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(15),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.withAlpha(30), width: 1),
        ),
        child: Row(
          children: [
            // 图标
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.blue.withAlpha(20),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                isBarcode ? Icons.qr_code : Icons.keyboard,
                color: Colors.blue,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),

            // 文本信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    result.title,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (result.subtitle != null)
                    Text(
                      result.subtitle!,
                      style: const TextStyle(fontSize: 13, color: Colors.grey),
                    ),
                ],
              ),
            ),

            // 相似度
            if (!isBarcode)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getSimilarityColor(result.similarity).withAlpha(30),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$similarityPercent%',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getSimilarityColor(result.similarity),
                  ),
                ),
              ),

            // 箭头
            const SizedBox(width: 8),
            const Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  // 根据相似度获取颜色
  Color _getSimilarityColor(double similarity) {
    if (similarity >= 0.9) {
      return Colors.green;
    } else if (similarity >= 0.7) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
