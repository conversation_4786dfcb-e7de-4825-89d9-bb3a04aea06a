import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:inventory_app_recode/core/config/app_routes.dart';
import 'package:inventory_app_recode/features/auth/view/pages/login_page.dart';
import 'package:inventory_app_recode/features/auth/view/pages/reset_password_page.dart';
import 'package:inventory_app_recode/features/auth/view_models/auth_view_model.dart';
import 'package:inventory_app_recode/features/home/<USER>/pages/home_page.dart';
import 'package:inventory_app_recode/features/splash/view/splash_page.dart';
import 'package:inventory_app_recode/main_container.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: 'http://127.0.0.1:54321',
    anonKey:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
  );
  runApp(ProviderScope(child: const MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      onGenerateRoute: AppRoutes.generateRoute,
      title: 'Inventory App',
      home: const MainContainer(),
    );
  }
}
