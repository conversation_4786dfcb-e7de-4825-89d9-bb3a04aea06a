import 'package:flutter/material.dart';
import 'package:inventory_app_recode/core/widgets/custom_bottom_navigation_bar.dart';
import 'package:inventory_app_recode/core/widgets/floating_message.dart';
import 'package:inventory_app_recode/features/home/<USER>/pages/home_page.dart';

class MainContainer extends StatefulWidget {
  final int initialIndex;

  const MainContainer({super.key, this.initialIndex = 0});

  @override
  State<MainContainer> createState() => _MainContainerState();
}

class _MainContainerState extends State<MainContainer> {
  late int _currentIndex;
  late PageController _pageController;
  late List<Widget> _pages;
  DateTime? _lastPressedAt;

  // 教程相关的Key
  final GlobalKey _homeKey = GlobalKey();
  final GlobalKey _searchKey = GlobalKey();
  final GlobalKey _scanKey = GlobalKey();
  final GlobalKey _addItemKey = GlobalKey();
  final GlobalKey _settingsKey = GlobalKey();
  late List<GlobalKey> _showcaseKeys;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);

    // 初始化页面
    _pages = [const HomePage()];

    // Initialize showcase keys list
    _showcaseKeys = [_homeKey, _searchKey, _scanKey, _addItemKey, _settingsKey];

    // 只有在用户首次登录时才显示教程
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget _buildBottomNavigationBar() {
    return CustomBottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: _onTabTapped,
      homeKey: _homeKey,
      searchKey: _searchKey,
      scanKey: _scanKey,
      addItemKey: _addItemKey,
      settingsKey: _settingsKey,
    );
  }

  void _onTabTapped(int index) {
    // 如果点击的是当前页面，不做任何操作
    if (index == _currentIndex) return;

    setState(() {
      _currentIndex = index;
    });

    // 使用无动画切换页面，减少卡顿
    _pageController.jumpToPage(index);
  }

  // 处理返回按钮事件
  Future<bool> _onWillPop() async {
    final now = DateTime.now();
    if (_lastPressedAt == null ||
        now.difference(_lastPressedAt!) > const Duration(seconds: 2)) {
      // 第一次点击返回按钮，显示提示
      _lastPressedAt = now;
      showFloatingMessage(
        context,
        message: '再按一次退出应用',
        icon: Icons.exit_to_app,
      );
      return false;
    }
    // 两秒内再次点击返回按钮，退出应用
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) async {
        if (didPop) return;
        final shouldPop = await _onWillPop();
        if (shouldPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(), // 禁用滑动切换
          children: _pages,
          onPageChanged: (index) {
            if (_currentIndex != index) {
              setState(() {
                _currentIndex = index;
              });
            }
          },
        ),
        bottomNavigationBar: _buildBottomNavigationBar(),
      ),
    );
  }
}
